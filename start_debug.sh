#!/bin/bash

# GAIA Service Debug Launcher
# Quick script to start the service in debug mode

echo "🐛 GAIA Service Debug Launcher"
echo "=============================="

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo "❌ Python not found. Please install Python."
    exit 1
fi

# Check if the service files exist
if [ ! -f "gaia_service/main.py" ]; then
    echo "❌ GAIA service files not found. Please run from project root."
    exit 1
fi

# Set debug environment
export DEBUG=true
export LOG_LEVEL=DEBUG
export HOST=127.0.0.1
export PORT=8000
export PYTHONPATH=$(pwd)
export PYTHONUNBUFFERED=1

echo "✅ Environment configured for debugging"
echo "🌐 Service will start at: http://127.0.0.1:8000"
echo "📚 API docs will be at: http://127.0.0.1:8000/docs"
echo ""
echo "Press Ctrl+C to stop the service"
echo "=============================="

# Start the service
python simple_debug.py
