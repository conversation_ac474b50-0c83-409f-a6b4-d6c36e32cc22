# GAIA Service Debug Guide

这个文档说明如何调试GAIA评估服务。

## 🚀 快速开始

### 方法1: 使用简单调试脚本（推荐）

```bash
# 最简单的调试方式
python simple_debug.py
```

这会启动服务在 `http://127.0.0.1:8000`，并自动设置调试环境。

### 方法2: 使用VS Code调试配置

1. 在VS Code中打开项目
2. 按 `F5` 或者点击 "Run and Debug" 面板
3. 选择 "Debug GAIA Service" 配置
4. 服务将在调试模式下启动在 `http://127.0.0.1:8000`

### 方法3: 使用VS Code任务

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择 "Start GAIA Service (Debug)"

### 方法4: 使用高级调试脚本

```bash
# 基本调试模式
python debug_gaia_service.py

# 自定义端口
python debug_gaia_service.py --port 8001

# 启用自动重载
python debug_gaia_service.py --reload

# 启用远程调试器
python debug_gaia_service.py --attach-debugger
```

### 方法5: 直接运行服务

```bash
# 设置调试环境变量
export DEBUG=true
export LOG_LEVEL=DEBUG
export HOST=127.0.0.1
export PORT=8000

# 运行服务
python gaia_service/main.py
```

## 🔧 调试配置说明

### VS Code Launch 配置

项目包含以下调试配置：

1. **Debug GAIA Service**: 标准调试模式，端口8000
2. **Debug GAIA Service (Custom Port)**: 自定义端口8001，避免端口冲突
3. **Debug GAIA Service (Production Mode)**: 生产模式调试
4. **Debug GAIA Service (Attach to Running Process)**: 附加到运行中的进程

### 环境变量

调试模式下会设置以下环境变量：

```bash
DEBUG=true                    # 启用调试模式
LOG_LEVEL=DEBUG              # 详细日志输出
HOST=127.0.0.1              # 本地主机
PORT=8000                   # 服务端口
PYTHONPATH=${workspaceFolder} # Python路径
PYTHONUNBUFFERED=1          # 禁用Python输出缓冲
FASTAPI_DEBUG=true          # FastAPI调试模式
```

## 🐛 调试技巧

### 1. 设置断点

- 在VS Code中点击行号左侧设置断点
- 或在代码中添加 `import pdb; pdb.set_trace()`

### 2. 查看日志

调试模式下会输出详细的日志信息：

```bash
# 查看服务状态
curl http://127.0.0.1:8000/health

# 查看API文档
open http://127.0.0.1:8000/docs
```

### 3. 远程调试

如果需要远程调试：

```bash
# 安装debugpy
pip install debugpy

# 启用远程调试
python debug_gaia_service.py --attach-debugger

# 在VS Code中使用 "Attach to Running Process" 配置
```

### 4. 热重载

开发时启用自动重载：

```bash
python debug_gaia_service.py --reload
```

## 📁 重要文件

- `.vscode/launch.json`: VS Code调试配置
- `.vscode/settings.json`: VS Code项目设置
- `debug_gaia_service.py`: 调试启动脚本
- `gaia_service/main.py`: 服务主入口
- `gaia_service/config.py`: 配置管理
- `.env`: 环境变量配置文件

## 🔍 常见问题

### 端口被占用

```bash
# 查看端口占用
lsof -i :8000

# 使用不同端口
python simple_debug.py  # 会自动使用8000端口
python debug_gaia_service.py --port 8001
```

### 模块导入错误

如果遇到导入错误，尝试：

1. 使用简单调试脚本：`python simple_debug.py`
2. 确保PYTHONPATH设置正确：
   ```bash
   export PYTHONPATH=/path/to/your/project
   ```
3. 检查Python环境是否正确

### API密钥缺失

检查 `.env` 文件中的API密钥配置：

```bash
OPENAI_API_KEY=your_openai_key
GOOGLE_API_KEY=your_google_key
SERPAPI_API_KEY=your_serpapi_key
```

### 服务启动失败

1. 检查依赖是否安装：`pip install -r requirements.txt`
2. 检查.env文件是否存在
3. 使用简单调试脚本：`python simple_debug.py`
4. 查看详细错误信息

### 循环导入错误

如果遇到camel模块的循环导入错误：

1. 使用 `simple_debug.py` 而不是 `debug_gaia_service.py`
2. 确保所有依赖都正确安装
3. 重启Python环境

## 📊 监控和测试

### 健康检查

```bash
curl http://127.0.0.1:8000/health
```

### API测试

```bash
# 测试根端点
curl http://127.0.0.1:8000/

# 查看API文档
open http://127.0.0.1:8000/docs
```

### 日志监控

调试模式下日志会输出到控制台，你可以：

1. 在VS Code的调试控制台查看
2. 在终端中查看实时输出
3. 配置文件日志（在config.py中设置LOG_FILE）

## 🎯 下一步

1. 设置断点开始调试
2. 查看API文档了解接口
3. 测试评估功能
4. 根据需要调整配置

Happy debugging! 🐛✨
