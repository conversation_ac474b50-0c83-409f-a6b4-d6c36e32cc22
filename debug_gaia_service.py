#!/usr/bin/env python3
"""
Debug launcher for GAIA Evaluation Service

This script provides an easy way to start the GAIA service in debug mode
with enhanced logging and debugging capabilities.
"""

import os
import sys
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_debug_environment():
    """Setup environment variables for debugging."""
    debug_env = {
        "DEBUG": "true",
        "LOG_LEVEL": "DEBUG",
        "HOST": "127.0.0.1",
        "PORT": "8000",
        "PYTHONPATH": str(project_root),
        # Enable Python debugging features
        "PYTHONDEBUG": "1",
        "PYTHONUNBUFFERED": "1",
        # FastAPI debug mode
        "FASTAPI_DEBUG": "true",
    }
    
    # Set environment variables
    for key, value in debug_env.items():
        os.environ[key] = value
        print(f"Set {key}={value}")


def main():
    """Main debug launcher."""
    parser = argparse.ArgumentParser(description="Debug GAIA Evaluation Service")
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="Port to run the service on (default: 8000)"
    )
    parser.add_argument(
        "--host", 
        type=str, 
        default="127.0.0.1", 
        help="Host to bind the service to (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--log-level", 
        type=str, 
        default="DEBUG", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level (default: DEBUG)"
    )
    parser.add_argument(
        "--attach-debugger",
        action="store_true",
        help="Enable remote debugger attachment on port 5678"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload on code changes"
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🐛 GAIA Evaluation Service - Debug Mode")
    print("=" * 60)
    
    # Setup debug environment
    setup_debug_environment()
    
    # Override with command line arguments
    os.environ["HOST"] = args.host
    os.environ["PORT"] = str(args.port)
    os.environ["LOG_LEVEL"] = args.log_level
    
    print(f"🌐 Service will run on: http://{args.host}:{args.port}")
    print(f"📚 API docs will be at: http://{args.host}:{args.port}/docs")
    print(f"📊 Health check at: http://{args.host}:{args.port}/health")
    print(f"📝 Log level: {args.log_level}")
    
    # Setup remote debugger if requested
    if args.attach_debugger:
        try:
            import debugpy
            debugpy.listen(("localhost", 5678))
            print("🔌 Remote debugger listening on localhost:5678")
            print("   You can now attach a debugger to this process")
        except ImportError:
            print("⚠️  debugpy not installed. Install with: pip install debugpy")
            print("   Remote debugging will not be available")
    
    print("=" * 60)
    print("🚀 Starting service...")
    print("   Press Ctrl+C to stop")
    print("=" * 60)
    
    try:
        if args.reload:
            # Use uvicorn with reload for development
            import uvicorn
            uvicorn.run(
                "gaia_service.main:run_service_with_api",
                host=args.host,
                port=args.port,
                reload=True,
                reload_dirs=[str(project_root / "gaia_service")],
                log_level=args.log_level.lower(),
                access_log=True
            )
        else:
            # Use the standard service launcher
            from gaia_service.main import run_service_with_api
            run_service_with_api()
            
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
    except Exception as e:
        print(f"\n❌ Service failed to start: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
