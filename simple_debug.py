#!/usr/bin/env python3
"""
Simple debug launcher for GAIA Evaluation Service

This is a simplified version that directly runs the service
without complex import handling.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Simple debug launcher."""
    # Set debug environment
    env = os.environ.copy()
    env.update({
        "DEBUG": "true",
        "LOG_LEVEL": "DEBUG",
        "HOST": "127.0.0.1",
        "PORT": "8000",
        "PYTHONPATH": str(Path.cwd()),
        "PYTHONUNBUFFERED": "1"
    })
    
    print("🐛 Starting GAIA Service in Debug Mode")
    print("=" * 50)
    print("🌐 Service URL: http://127.0.0.1:8000")
    print("📚 API Docs: http://127.0.0.1:8000/docs")
    print("📊 Health: http://127.0.0.1:8000/health")
    print("=" * 50)
    print("Press Ctrl+C to stop")
    print()
    
    try:
        # Run the service directly
        subprocess.run([
            sys.executable, 
            "gaia_service/main.py"
        ], env=env)
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
