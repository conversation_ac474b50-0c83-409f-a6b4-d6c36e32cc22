
"""
GAIA Evaluation Service Main Application

This module provides the main entry point for the GAIA evaluation service,
including service initialization, startup, and shutdown procedures.
"""

import asyncio
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI
from loguru import logger

from .config import load_config, setup_logging, get_model_config
from .core import GAIAEvaluationService
from .api import GAIAServiceAPI




# Global service instances
service_instances: Dict[str, Any] = {}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI lifespan context manager for startup and shutdown.
    
    Args:
        app: FastAPI application instance
    """
    # Startup
    logger.info("Starting GAIA Evaluation Service...")
    
    try:
        # Load configuration
        config = load_config()
        setup_logging(config)
        
        logger.info(f"Service configuration loaded: {config.service_name} v{config.service_version}")
        
        # Create evaluation service without default workforce
        # All workforce will be created dynamically based on client LLM config
        logger.info("Creating evaluation service...")
        evaluation_service = GAIAEvaluationService(
            workforce=None,
            temp_dir=config.temp_dir
        )
        
        # Store service instances globally
        service_instances["config"] = config
        service_instances["evaluation_service"] = evaluation_service
        
        # Add service instance to app state
        app.state.evaluation_service = evaluation_service
        app.state.config = config
        
        logger.success("GAIA Evaluation Service started successfully!")
        logger.info(f"Service available at http://{config.host}:{config.port}")
        logger.info(f"API documentation at http://{config.host}:{config.port}/docs")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down GAIA Evaluation Service...")
        
        # Stop workforce if running
        if "workforce" in service_instances:
            workforce = service_instances["workforce"]
            try:
                if hasattr(workforce, 'is_running') and workforce.is_running():
                    workforce.stop()
                    logger.info("Workforce stopped")
            except Exception as e:
                logger.error(f"Error stopping workforce: {e}")
        
        # Clean up service instances
        service_instances.clear()
        
        logger.info("Service shutdown complete")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    # Create FastAPI app with lifespan
    app = FastAPI(
        title="GAIA Evaluation Service",
        description="Asynchronous service for evaluating GAIA benchmark queries with detailed execution traces",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # This will be set during startup
    app.state.evaluation_service = None
    app.state.config = None
    
    return app


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def main():
    """Main entry point for the service."""
    try:
        # Setup signal handlers
        setup_signal_handlers()
        
        # Load initial configuration for server settings
        config = load_config()
        setup_logging(config)
        
        logger.info("Starting GAIA Evaluation Service...")
        
        # Create the FastAPI app
        app = create_app()
        
        # Create and mount the API
        # Note: We'll create the API routes after the service is initialized in lifespan
        @app.get("/")
        async def root():
            return {
                "service": "GAIA Evaluation Service",
                "version": "1.0.0",
                "status": "running",
                "docs": "/docs"
            }
        
        @app.get("/health")
        async def health():
            if hasattr(app.state, 'evaluation_service') and app.state.evaluation_service:
                stats = app.state.evaluation_service.get_service_stats()
                return {
                    "status": "healthy",
                    "version": "1.0.0",
                    **stats
                }
            return {"status": "starting"}
        
        # We'll add the full API routes after service initialization
        # This is a simplified version for the main startup
        
        # Run the server
        uvicorn.run(
            app,
            host=config.host,
            port=config.port,
            workers=1,  # Use 1 worker for now to avoid complexity with shared state
            log_level=config.log_level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("Service interrupted by user")
    except Exception as e:
        logger.error(f"Service failed to start: {e}")
        sys.exit(1)


def run_service_with_api():
    """Run the service with full API integration."""
    try:
        # Load configuration
        config = load_config()
        setup_logging(config)
        
        logger.info("Starting GAIA Evaluation Service with full API...")
        
        # Create evaluation service without default workforce
        # All workforce will be created dynamically based on client LLM config
        evaluation_service = GAIAEvaluationService(
            workforce=None,
            temp_dir=config.temp_dir
        )
        
        # Create API
        api = GAIAServiceAPI(evaluation_service)
        app = api.app
        
        # Add configuration to app state
        app.state.config = config
        app.state.evaluation_service = evaluation_service
        
        logger.success("Service initialized successfully!")
        
        # Run the server
        uvicorn.run(
            app,
            host=config.host,
            port=config.port,
            workers=1,
            log_level=config.log_level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("Service interrupted by user")
    except Exception as e:
        logger.error(f"Service failed to start: {e}")
        sys.exit(1)
    finally:
        # Cleanup - workforce cleanup is now handled by the evaluation service
        pass


if __name__ == "__main__":
    # Use the simpler version for now
    run_service_with_api()
