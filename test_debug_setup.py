#!/usr/bin/env python3
"""
Test script to verify debug setup is working correctly.
"""

import os
import sys
import requests
import time
import subprocess
from pathlib import Path

def test_debug_environment():
    """Test if debug environment is set up correctly."""
    print("🔍 Testing debug environment setup...")
    
    # Check if required files exist
    required_files = [
        ".vscode/launch.json",
        ".vscode/settings.json", 
        "debug_gaia_service.py",
        "gaia_service/main.py",
        "gaia_service/config.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print("✅ All required debug files are present")
        return True


def test_python_imports():
    """Test if Python can import the service modules."""
    print("\n🐍 Testing Python imports...")
    
    try:
        # Test basic imports
        sys.path.insert(0, str(Path.cwd()))
        
        from gaia_service.config import load_config, ServiceConfig
        from gaia_service.main import create_app
        print("✅ Basic imports successful")
        
        # Test configuration loading
        config = load_config()
        print(f"✅ Configuration loaded: {config.service_name}")
        
        # Test app creation
        app = create_app()
        print("✅ FastAPI app creation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def test_debug_script():
    """Test if the debug script can be executed."""
    print("\n🚀 Testing debug script...")
    
    try:
        # Test help output
        result = subprocess.run(
            [sys.executable, "debug_gaia_service.py", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Debug script help command works")
            return True
        else:
            print(f"❌ Debug script failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Debug script test error: {e}")
        return False


def test_service_startup():
    """Test if the service can start up (quick test)."""
    print("\n🌐 Testing service startup (quick test)...")
    
    try:
        # Start service in background for a quick test
        env = os.environ.copy()
        env.update({
            "DEBUG": "true",
            "LOG_LEVEL": "INFO",
            "HOST": "127.0.0.1",
            "PORT": "8999",  # Use different port to avoid conflicts
            "PYTHONPATH": str(Path.cwd())
        })
        
        process = subprocess.Popen(
            [sys.executable, "debug_gaia_service.py", "--port", "8999"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Wait a bit for startup
        time.sleep(3)
        
        # Check if service is responding
        try:
            response = requests.get("http://127.0.0.1:8999/", timeout=5)
            if response.status_code == 200:
                print("✅ Service startup successful")
                success = True
            else:
                print(f"❌ Service returned status {response.status_code}")
                success = False
        except requests.exceptions.RequestException as e:
            print(f"❌ Service not responding: {e}")
            success = False
        
        # Cleanup
        process.terminate()
        process.wait(timeout=5)
        
        return success
        
    except Exception as e:
        print(f"❌ Service startup test error: {e}")
        return False


def main():
    """Run all debug setup tests."""
    print("🧪 GAIA Service Debug Setup Test")
    print("=" * 50)
    
    tests = [
        ("Environment Setup", test_debug_environment),
        ("Python Imports", test_python_imports),
        ("Debug Script", test_debug_script),
        ("Service Startup", test_service_startup)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Debug setup is ready.")
        print("\nNext steps:")
        print("1. Open VS Code")
        print("2. Press F5 to start debugging")
        print("3. Or run: python debug_gaia_service.py")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
